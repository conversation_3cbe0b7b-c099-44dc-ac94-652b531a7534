components:
  responses:
    BadRequest:
      description: Requête invalide
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Données de requête invalides"
              errors:
                type: array
                items:
                  type: object
                  properties:
                    field:
                      type: string
                      example: "email"
                    message:
                      type: string
                      example: "Format d'email invalide"

    Unauthorized:
      description: Non autorisé - Token manquant ou invalide
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Token d'authentification requis"
          examples:
            missing_token:
              summary: Token manquant
              value:
                status: "error"
                message: "Token d'authentification requis"
            invalid_token:
              summary: Token invalide
              value:
                status: "error"
                message: "Token invalide ou expiré"

    Forbidden:
      description: Accès interdit - Permissions insuffisantes
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Accès interdit"

    NotFound:
      description: Ressource non trouvée
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Ressource non trouvée"

    Conflict:
      description: Conflit - La ressource existe déjà
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "La ressource existe déjà"

    InternalServerError:
      description: Erreur interne du serveur
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Erreur interne du serveur"

  schemas:
    Pagination:
      type: object
      properties:
        currentPage:
          type: integer
          description: Page actuelle
          example: 1
        totalPages:
          type: integer
          description: Nombre total de pages
          example: 5
        totalItems:
          type: integer
          description: Nombre total d'éléments
          example: 47
        itemsPerPage:
          type: integer
          description: Nombre d'éléments par page
          example: 10
        hasNextPage:
          type: boolean
          description: Indique s'il y a une page suivante
          example: true
        hasPreviousPage:
          type: boolean
          description: Indique s'il y a une page précédente
          example: false
      required:
        - currentPage
        - totalPages
        - totalItems
        - itemsPerPage
        - hasNextPage
        - hasPreviousPage

    StandardResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["success", "error"]
          description: Statut de la réponse
          example: "success"
        message:
          type: string
          description: Message descriptif
          example: "Opération réussie"
        data:
          type: object
          description: Données de la réponse
      required:
        - status

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          enum: ["error"]
          example: "error"
        message:
          type: string
          description: Message d'erreur
          example: "Une erreur est survenue"
        errors:
          type: array
          description: Détails des erreurs (optionnel)
          items:
            type: object
            properties:
              field:
                type: string
                description: Champ concerné par l'erreur
              message:
                type: string
                description: Message d'erreur spécifique
      required:
        - status
        - message
