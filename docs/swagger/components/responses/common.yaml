components:
  responses:
    BadRequest:
      description: Requête invalide
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Données de requête invalides"
              errors:
                type: array
                items:
                  type: object
                  properties:
                    field:
                      type: string
                      example: "email"
                    message:
                      type: string
                      example: "Format d'email invalide"

    Unauthorized:
      description: Non autorisé - Token manquant ou invalide
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Token d'authentification requis"
          examples:
            missing_token:
              summary: Token manquant
              value:
                status: "error"
                message: "Token d'authentification requis"
            invalid_token:
              summary: Token invalide
              value:
                status: "error"
                message: "Token invalide ou expiré"

    Forbidden:
      description: Accès interdit - Permissions insuffisantes
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Accès interdit"

    NotFound:
      description: Ressource non trouvée
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Ressource non trouvée"

    Conflict:
      description: Conflit - La ressource existe déjà
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "La ressource existe déjà"

    InternalServerError:
      description: Erreur interne du serveur
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                example: "error"
              message:
                type: string
                example: "Erreur interne du serveur"

  schemas:
    PaginatedResponse:
      type: object
      properties:
        page:
          type: integer
          description: Page actuelle
          example: 1
        limit:
          type: integer
          description: Nombre d'éléments par page
          example: 10
        total:
          type: integer
          description: Nombre total d'éléments
          example: 47
        totalPages:
          type: integer
          description: Nombre total de pages
          example: 5
        hasNextPage:
          type: boolean
          description: Indique s'il y a une page suivante
          example: true
        hasPrevPage:
          type: boolean
          description: Indique s'il y a une page précédente
          example: false
        data:
          type: array
          description: Données paginées
          items:
            type: object
      required:
        - page
        - limit
        - total
        - totalPages
        - hasNextPage
        - hasPrevPage
        - data

    StandardResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indique si la requête a réussi
          example: true
        status:
          type: integer
          description: Code de statut HTTP
          example: 200
        message:
          type: string
          description: Message descriptif
          example: "Opération réussie"
        data:
          type: object
          description: Données de la réponse
      required:
        - success
        - status
        - message

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Indique si la requête a échoué
          example: false
        status:
          type: integer
          description: Code de statut HTTP d'erreur
          example: 400
        message:
          type: string
          description: Message d'erreur
          example: "Une erreur est survenue"
        errors:
          type: array
          description: Détails des erreurs (optionnel)
          nullable: true
          items:
            type: object
            properties:
              field:
                type: string
                description: Champ concerné par l'erreur
              message:
                type: string
                description: Message d'erreur spécifique
      required:
        - success
        - status
        - message
